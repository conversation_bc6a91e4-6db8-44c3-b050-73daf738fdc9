# 采集任务运行结果统计平台

一个基于Flask和Bootstrap的前后端分离项目，用于统计和管理采集任务的运行结果。

## 功能特性

### 📊 首页统计
- 总采集站点数统计（根据paichong去重）
- 当天已采集数统计
- 未采集统计
- 最近任务预览

### 📋 任务列表管理
- 分页显示采集任务列表（支持20/50/100条每页）
- 多条件筛选：任务名称、时间区间、服务器、文件夹
- 多字段排序：开始时间、结束时间、距今时间
- 数据导出功能（CSV格式）
- 任务详情查看：点击"查看详情"按钮进入单个任务的详细统计页面

### 📊 任务详情统计
- 根据computer、file、runid确定同一采集任务
- **页面布局**（按显示顺序）：
  1. 任务基本信息：任务名称、服务器、文件夹、运行次数
  2. 运行趋势图：ECharts堆叠面积图展示7个数据系列的运行趋势
  3. 统计汇总：总成功/失败网址数、内容成功/失败数、发布成功/失败数（基于所有记录计算）
  4. 运行历史记录：默认按开始时间倒序显示最新100条记录
- **趋势图功能**：
  - 支持堆叠面积图和折线图两种显示模式
  - 数据系列：成功网址数、失败网址数、重复网址数、内容成功数、内容失败数、发布成功数、发布失败数
  - 交互功能：数据缩放、保存图片、图例筛选等
- **性能优化**：
  - 分页加载：支持"加载更多"功能，可动态加载更多历史记录
  - 记录限制：对于成百上千条记录的任务，采用限制显示策略保证响应速度

### 📈 数据展示
- 成功网址数、失败网址数、重复网址数
- 内容成功数、内容失败数
- 发布成功数、发布失败数
- 开始时间、结束时间、距今时间
- 服务器、文件夹信息

## 技术栈

- **后端**: Flask 2.3.3
- **前端**: HTML5 + Bootstrap 5.3.0 + JavaScript
- **图表库**: ECharts 5.4.3
- **数据库**: MySQL 8.0+
- **ORM**: SQLAlchemy
- **其他**: Flask-CORS, PyMySQL

## 项目结构

```
caiji/
├── app.py                 # Flask应用主文件
├── config.py             # 配置文件
├── database.py           # 数据库初始化
├── models.py             # 数据模型
├── routes.py             # API路由
├── init_db.py            # 数据库初始化脚本
├── requirements.txt      # Python依赖
├── .env                  # 环境变量配置
├── templates/            # HTML模板
│   ├── base.html
│   ├── index.html
│   ├── list.html
│   ├── task_detail.html
│   ├── 404.html
│   └── 500.html
└── static/               # 静态文件
    ├── css/
    │   └── style.css
    └── js/
        ├── common.js
        ├── index.js
        ├── list.js
        └── task_detail.js
```

## 快速开始

### 1. 环境要求

- Python 3.7+
- MySQL 8.0+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制并编辑 `.env` 文件：

```bash
# 数据库配置 - MySQL
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=caiji_db

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 服务器配置
PORT=5011
```

### 4. 初始化MySQL数据库

```bash
# 第一步：创建MySQL数据库
python init_mysql.py

# 第二步：创建数据表和示例数据
python init_db.py
```

### 5. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5011` 启动。

## API接口

### 统计数据接口
```
GET /api/statistics
```

### 任务列表接口
```
GET /api/tasks?page=1&per_page=20&sort_by=StartTime&sort_order=desc
```

支持的查询参数：
- `page`: 页码（默认1）
- `per_page`: 每页数量（20/50/100）
- `sort_by`: 排序字段（StartTime/EndTime/time_since_start）
- `sort_order`: 排序方向（asc/desc）
- `job_name`: 任务名称筛选
- `start_time_from`: 开始时间筛选（从）
- `start_time_to`: 开始时间筛选（到）
- `computer`: 服务器筛选
- `file`: 文件夹筛选

### 任务详情接口
```
GET /api/task-detail?computer=服务器名&file=文件夹名&runid=任务ID&limit=100
```

支持的查询参数：
- `computer`: 服务器名称（必需）
- `file`: 文件夹名称（必需）
- `runid`: 采集任务ID（必需）
- `limit`: 显示记录数限制（可选，默认100，最大1000）

### 筛选选项接口
```
GET /api/filters/options
```

## 数据库表结构

### collection_tasks 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键 |
| runid | int | 采集任务id |
| JobName | varchar(255) | 采集任务名字 |
| JobId | int | 采集任务id |
| UrlSuccessCount | varchar(50) | 成功网址数 |
| UrlFailCount | varchar(50) | 失败网址数 |
| UrlRepeatCount | varchar(50) | 重复网址数 |
| ContentSuccessCount | varchar(50) | 内容成功数 |
| ContentFailCount | varchar(50) | 内容失败数 |
| OutputSuccessCount | varchar(50) | 发布成功数 |
| OutputFailCount | varchar(50) | 发布失败数 |
| EndTime | datetime | 结束时间 |
| StartTime | datetime | 开始时间 |
| computer | varchar(255) | 服务器 |
| file | varchar(255) | 文件夹 |
| collecttime | timestamp | 收集时间 |
| paichong | varchar(500) | 排重（任务id + 服务器 + 文件夹名） |
| fabu | int | 未提醒0，已提醒1 |

## 生产环境部署

### 1. 使用Gunicorn部署

安装Gunicorn：
```bash
pip install gunicorn
```

启动应用：
```bash
gunicorn -w 4 -b 0.0.0.0:5011 app:app
```

### 2. 使用Nginx反向代理

Nginx配置示例：
```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:5011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /static {
        alias /path/to/your/project/static;
        expires 30d;
    }
}
```

### 3. 环境变量配置

生产环境建议设置：
```bash
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your_production_secret_key
```

## 开发说明

### 添加新功能

1. 在 `models.py` 中定义数据模型
2. 在 `routes.py` 中添加API接口
3. 在 `templates/` 中创建HTML模板
4. 在 `static/js/` 中添加前端逻辑

### 代码规范

- 后端遵循PEP 8规范
- 前端使用ES6+语法
- 注释清晰，函数命名语义化

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
