#!/usr/bin/env python3
"""
生产环境部署脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def install_dependencies():
    """安装依赖"""
    print("正在安装Python依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✓ 依赖安装完成")
    except subprocess.CalledProcessError:
        print("错误: 依赖安装失败")
        sys.exit(1)

def setup_environment():
    """设置环境变量"""
    env_file = Path(".env")
    env_prod_file = Path(".env.production")
    
    if not env_file.exists():
        if env_prod_file.exists():
            print("正在复制生产环境配置...")
            with open(env_prod_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✓ 环境配置文件已创建")
            print("⚠️  请编辑 .env 文件，设置正确的数据库密码和密钥")
        else:
            print("错误: 找不到环境配置文件")
            sys.exit(1)
    else:
        print("✓ 环境配置文件已存在")

def init_database():
    """初始化MySQL数据库"""
    print("正在初始化MySQL数据库...")

    # 检查是否需要创建数据库
    choice = input("是否需要创建MySQL数据库? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        try:
            subprocess.run([sys.executable, "init_mysql.py"], check=True)
            print("✓ MySQL数据库创建完成")
        except subprocess.CalledProcessError:
            print("错误: MySQL数据库创建失败")
            print("请检查MySQL服务和连接配置")
            sys.exit(1)

    # 创建数据表和示例数据
    try:
        subprocess.run([sys.executable, "init_db.py"], check=True)
        print("✓ 数据表和示例数据初始化完成")
    except subprocess.CalledProcessError:
        print("错误: 数据表初始化失败")
        print("请检查MySQL数据库连接配置")
        sys.exit(1)

def create_systemd_service():
    """创建systemd服务文件（Linux）"""
    if os.name != 'posix':
        print("⚠️  非Linux系统，跳过systemd服务创建")
        return
    
    current_dir = os.getcwd()
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=Caiji Statistics Platform
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={current_dir}
Environment=PATH={os.path.dirname(python_path)}
ExecStart={python_path} -m gunicorn -w 4 -b 0.0.0.0:5011 app:app
Restart=always

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/caiji-stats.service"
    try:
        with open(service_file, 'w') as f:
            f.write(service_content)
        print(f"✓ systemd服务文件已创建: {service_file}")
        print("运行以下命令启用服务:")
        print("sudo systemctl daemon-reload")
        print("sudo systemctl enable caiji-stats")
        print("sudo systemctl start caiji-stats")
    except PermissionError:
        print("⚠️  需要root权限创建systemd服务文件")
        print("请手动创建服务文件或使用sudo运行此脚本")

def create_nginx_config():
    """创建Nginx配置文件"""
    nginx_config = """server {
    listen 80;
    server_name your_domain.com;  # 请修改为您的域名

    location / {
        proxy_pass http://127.0.0.1:5011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/project/static;  # 请修改为实际路径
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # 日志配置
    access_log /var/log/nginx/caiji_access.log;
    error_log /var/log/nginx/caiji_error.log;
}
"""
    
    config_file = "nginx_caiji.conf"
    with open(config_file, 'w') as f:
        f.write(nginx_config)
    
    print(f"✓ Nginx配置文件已创建: {config_file}")
    print("请将此文件复制到 /etc/nginx/sites-available/ 并创建软链接到 sites-enabled/")

def install_gunicorn():
    """安装Gunicorn"""
    print("正在安装Gunicorn...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "gunicorn"], check=True)
        print("✓ Gunicorn安装完成")
    except subprocess.CalledProcessError:
        print("错误: Gunicorn安装失败")
        sys.exit(1)

def main():
    """主函数"""
    print("=== 采集任务统计平台部署脚本 ===\n")
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    install_dependencies()
    
    # 安装Gunicorn
    install_gunicorn()
    
    # 设置环境
    setup_environment()
    
    # 初始化数据库
    init_database()
    
    # 创建服务文件
    create_systemd_service()
    
    # 创建Nginx配置
    create_nginx_config()
    
    print("\n=== 部署完成 ===")
    print("接下来的步骤:")
    print("1. 编辑 .env 文件，设置正确的数据库密码和密钥")
    print("2. 配置Nginx（如果使用）")
    print("3. 启动应用:")
    print("   开发环境: python app.py")
    print("   生产环境: gunicorn -w 4 -b 0.0.0.0:5011 app:app")
    print("4. 访问 http://localhost:5011 查看应用")

if __name__ == '__main__':
    main()
