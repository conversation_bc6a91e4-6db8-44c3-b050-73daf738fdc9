import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST', '127.0.0.1')
    DB_PORT = os.environ.get('DB_PORT', '3306')
    DB_USER = os.environ.get('DB_USER', 'caiji_db')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'GMDSYrcE5fnZeeaa')
    DB_NAME = os.environ.get('DB_NAME', 'caiji_db')
    
    # SQLAlchemy配置 - 使用MySQL数据库
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'pool_size': 10,
        'max_overflow': 20,
        'connect_args': {
            'charset': 'utf8mb4',
            'connect_timeout': 60,
            'read_timeout': 30,
            'write_timeout': 30
        }
    }
    
    # 服务器配置
    PORT = int(os.environ.get('PORT', 5011))
    
    # 分页配置
    ITEMS_PER_PAGE_DEFAULT = 20
    ITEMS_PER_PAGE_OPTIONS = [20, 50, 100]
