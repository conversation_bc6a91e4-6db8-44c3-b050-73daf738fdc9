#!/usr/bin/env python3
"""
MySQL 8.0数据库初始化脚本
"""

import pymysql
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_database():
    """创建MySQL数据库"""
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', '3306'))
    DB_USER = os.environ.get('DB_USER', 'root')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
    DB_NAME = os.environ.get('DB_NAME', 'caiji_db')
    
    print(f"正在连接MySQL服务器 {DB_HOST}:{DB_PORT}...")
    
    try:
        # 连接MySQL 8.0服务器（不指定数据库）
        connection = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci',
            autocommit=True,
            connect_timeout=30
        )
        
        print("✅ MySQL服务器连接成功")
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{DB_NAME}'")
            result = cursor.fetchone()
            
            if result:
                print(f"📋 数据库 '{DB_NAME}' 已存在")
                
                # 询问是否重新创建
                choice = input(f"是否删除并重新创建数据库 '{DB_NAME}'? (y/n): ").strip().lower()
                if choice in ['y', 'yes', '是']:
                    cursor.execute(f"DROP DATABASE {DB_NAME}")
                    print(f"🗑️ 已删除数据库 '{DB_NAME}'")
                else:
                    print("保持现有数据库")
                    connection.close()
                    return True
            
            # 创建数据库（MySQL 8.0优化）
            cursor.execute(f"""
                CREATE DATABASE {DB_NAME}
                CHARACTER SET utf8mb4
                COLLATE utf8mb4_unicode_ci
                DEFAULT ENCRYPTION='N'
            """)
            print(f"✅ 数据库 '{DB_NAME}' 创建成功")

            # 创建用户（如果需要）- MySQL 8.0兼容
            if DB_USER != 'root':
                try:
                    # MySQL 8.0 使用 caching_sha2_password 作为默认认证插件
                    # 为了兼容性，我们使用 mysql_native_password
                    cursor.execute(f"""
                        CREATE USER '{DB_USER}'@'%'
                        IDENTIFIED WITH mysql_native_password BY '{DB_PASSWORD}'
                    """)
                    cursor.execute(f"GRANT ALL PRIVILEGES ON {DB_NAME}.* TO '{DB_USER}'@'%'")
                    cursor.execute("FLUSH PRIVILEGES")
                    print(f"✅ 用户 '{DB_USER}' 创建成功并授权（使用mysql_native_password）")
                except pymysql.Error as e:
                    if "already exists" in str(e):
                        print(f"📋 用户 '{DB_USER}' 已存在")
                        # 尝试修改认证方式
                        try:
                            cursor.execute(f"""
                                ALTER USER '{DB_USER}'@'%'
                                IDENTIFIED WITH mysql_native_password BY '{DB_PASSWORD}'
                            """)
                            print(f"✅ 用户 '{DB_USER}' 认证方式已更新为mysql_native_password")
                        except pymysql.Error as alter_e:
                            print(f"⚠️ 用户认证方式更新警告: {alter_e}")
                    else:
                        print(f"⚠️ 用户创建警告: {e}")
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ MySQL连接失败: {e}")
        print("\n💡 请检查:")
        print("1. MySQL服务是否已启动")
        print("2. 用户名和密码是否正确")
        print("3. 主机和端口是否正确")
        print("4. 用户是否有创建数据库的权限")
        return False
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def test_connection():
    """测试数据库连接"""
    
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', '3306'))
    DB_USER = os.environ.get('DB_USER', 'root')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
    DB_NAME = os.environ.get('DB_NAME', 'caiji_db')
    
    try:
        connection = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci',
            autocommit=True,
            connect_timeout=30
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 数据库连接测试成功")
            print(f"📋 MySQL版本: {version[0]}")
            
            # 显示数据库信息
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()
            print(f"📋 当前数据库: {db_name[0]}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 数据表数量: {len(tables)}")
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== MySQL数据库初始化 ===\n")
    
    # 显示配置信息
    print("📋 数据库配置:")
    print(f"   主机: {os.environ.get('DB_HOST', 'localhost')}")
    print(f"   端口: {os.environ.get('DB_PORT', '3306')}")
    print(f"   用户: {os.environ.get('DB_USER', 'root')}")
    print(f"   数据库: {os.environ.get('DB_NAME', 'caiji_db')}")
    print()
    
    # 创建数据库
    if create_database():
        print("\n=== 测试数据库连接 ===")
        if test_connection():
            print("\n✅ MySQL数据库初始化完成！")
            print("\n📝 下一步:")
            print("1. 运行 'python init_db.py' 创建数据表和示例数据")
            print("2. 运行 'python app.py' 启动应用")
        else:
            print("\n❌ 数据库连接测试失败，请检查配置")
    else:
        print("\n❌ 数据库初始化失败")

if __name__ == '__main__':
    main()
