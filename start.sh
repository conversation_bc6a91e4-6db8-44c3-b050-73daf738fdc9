#!/bin/bash

echo "启动采集任务统计平台..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖..."
if ! python3 -c "import flask" &> /dev/null; then
    echo "正在安装依赖..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

# 检查MySQL数据库连接
echo "检查MySQL数据库连接..."
python3 -c "from app import app; from database import db; app.app_context().push(); db.engine.execute('SELECT 1')" >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: MySQL数据库连接失败"
    echo "请确保:"
    echo "1. MySQL服务已启动"
    echo "2. 数据库已创建 (运行 python3 init_mysql.py)"
    echo "3. .env文件中的数据库配置正确"
    exit 1
fi

# 启动应用
echo "启动应用..."
echo "应用将在 http://localhost:5011 启动"
echo "按 Ctrl+C 停止应用"
echo
python3 app.py
