# MySQL 8.0 安装和配置指南

本文档提供MySQL 8.0的安装和配置指南，用于采集任务运行结果统计平台。

## 📋 系统要求

- **操作系统**: Windows 10+, Ubuntu 18.04+, CentOS 7+, macOS 10.14+
- **内存**: 最少512MB，推荐2GB+
- **磁盘空间**: 最少200MB，推荐1GB+

## 🚀 安装MySQL 8.0

### Windows安装

1. **下载MySQL 8.0**
   - 访问 [MySQL官网](https://dev.mysql.com/downloads/mysql/)
   - 下载MySQL Community Server 8.0
   - 选择Windows (x86, 64-bit), MSI Installer

2. **安装步骤**
   ```
   1. 运行下载的MSI安装程序
   2. 选择"Custom"安装类型
   3. 选择以下组件：
      - MySQL Server 8.0
      - MySQL Workbench (可选，用于图形化管理)
   4. 配置MySQL Server：
      - Config Type: Development Computer
      - Connectivity: TCP/IP, Port 3306
      - Authentication Method: Use Legacy Authentication Method (推荐)
      - Root Password: 设置强密码
   5. 完成安装
   ```

3. **启动MySQL服务**
   ```cmd
   # 启动服务
   net start mysql80

   # 停止服务
   net stop mysql80
   ```

### Ubuntu/Debian安装

```bash
# 更新包列表
sudo apt update

# 安装MySQL 8.0
sudo apt install mysql-server-8.0

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### CentOS/RHEL安装

```bash
# 添加MySQL 8.0仓库
sudo rpm -Uvh https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL 8.0
sudo yum install mysql-community-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时root密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

### macOS安装

```bash
# 使用Homebrew安装
brew install mysql

# 启动MySQL服务
brew services start mysql

# 安全配置
mysql_secure_installation
```

## ⚙️ MySQL 8.0配置

### 1. 创建数据库用户

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE caiji_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（使用mysql_native_password认证）
CREATE USER 'caiji_user'@'%' IDENTIFIED WITH mysql_native_password BY 'your_password';

-- 授权
GRANT ALL PRIVILEGES ON caiji_db.* TO 'caiji_user'@'%';
FLUSH PRIVILEGES;

-- 验证用户
SELECT user, host, plugin FROM mysql.user WHERE user = 'caiji_user';
```

### 2. 配置文件优化

**Windows**: `C:\ProgramData\MySQL\MySQL Server 8.0\my.ini`
**Linux**: `/etc/mysql/mysql.conf.d/mysqld.cnf`
**macOS**: `/usr/local/etc/my.cnf`

```ini
[mysqld]
# 基本配置
port = 3306
bind-address = 0.0.0.0

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 200
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800

# 缓冲区配置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 认证插件（兼容性）
default_authentication_plugin = mysql_native_password

# 时区设置
default-time-zone = '+08:00'
```

### 3. 重启MySQL服务

```bash
# Windows
net stop mysql80
net start mysql80

# Linux
sudo systemctl restart mysql

# macOS
brew services restart mysql
```

## 🔧 项目配置

### 1. 更新.env文件

```bash
# 数据库配置 - MySQL 8.0
DB_HOST=localhost
DB_PORT=3306
DB_USER=caiji_user
DB_PASSWORD=your_password
DB_NAME=caiji_db

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 服务器配置
PORT=5011
```

### 2. 初始化数据库

```bash
# 安装Python依赖
pip install -r requirements.txt

# 创建数据库（如果使用root用户）
python init_mysql.py

# 创建数据表和示例数据
python init_db.py

# 测试数据库连接
python test_mysql.py
```

## 🧪 测试连接

### 命令行测试

```bash
# 测试MySQL连接
mysql -h localhost -P 3306 -u caiji_user -p caiji_db

# 显示数据库
SHOW DATABASES;

# 使用数据库
USE caiji_db;

# 显示数据表
SHOW TABLES;
```

### Python测试

```python
import pymysql

# 测试连接
connection = pymysql.connect(
    host='localhost',
    port=3306,
    user='caiji_user',
    password='your_password',
    database='caiji_db',
    charset='utf8mb4'
)

with connection.cursor() as cursor:
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"MySQL版本: {version[0]}")

connection.close()
```

## 🚨 常见问题

### 1. 认证插件问题

**错误**: `Authentication plugin 'caching_sha2_password' cannot be loaded`

**解决方案**:
```sql
-- 修改用户认证方式
ALTER USER 'caiji_user'@'%' IDENTIFIED WITH mysql_native_password BY 'your_password';
FLUSH PRIVILEGES;
```

### 2. 连接超时问题

**错误**: `Lost connection to MySQL server during query`

**解决方案**:
```ini
# 在my.cnf中增加
wait_timeout = 28800
interactive_timeout = 28800
max_allowed_packet = 64M
```

### 3. 字符集问题

**错误**: `Incorrect string value`

**解决方案**:
```sql
-- 检查字符集
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 修改数据库字符集
ALTER DATABASE caiji_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 权限问题

**错误**: `Access denied for user`

**解决方案**:
```sql
-- 检查用户权限
SHOW GRANTS FOR 'caiji_user'@'%';

-- 重新授权
GRANT ALL PRIVILEGES ON caiji_db.* TO 'caiji_user'@'%';
FLUSH PRIVILEGES;
```

## 📊 性能优化

### 1. 索引优化

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_computer_file_runid ON collection_tasks(computer, file, runid);
CREATE INDEX idx_start_time ON collection_tasks(StartTime);
CREATE INDEX idx_paichong ON collection_tasks(paichong);
```

### 2. 配置优化

```ini
# 针对采集任务统计平台的优化配置
[mysqld]
innodb_buffer_pool_size = 512M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
query_cache_type = 1
query_cache_size = 64M
```

## 🔒 安全配置

### 1. 用户权限最小化

```sql
-- 创建只读用户（用于报表查询）
CREATE USER 'caiji_readonly'@'%' IDENTIFIED WITH mysql_native_password BY 'readonly_password';
GRANT SELECT ON caiji_db.* TO 'caiji_readonly'@'%';

-- 创建备份用户
CREATE USER 'caiji_backup'@'localhost' IDENTIFIED WITH mysql_native_password BY 'backup_password';
GRANT SELECT, LOCK TABLES ON caiji_db.* TO 'caiji_backup'@'localhost';
```

### 2. 网络安全

```ini
# 限制网络访问
bind-address = 127.0.0.1  # 仅本地访问
# bind-address = 0.0.0.0  # 允许远程访问

# SSL配置（生产环境推荐）
ssl-ca = /path/to/ca.pem
ssl-cert = /path/to/server-cert.pem
ssl-key = /path/to/server-key.pem
```

## 📝 维护命令

```bash
# 备份数据库
mysqldump -u caiji_user -p caiji_db > caiji_backup.sql

# 恢复数据库
mysql -u caiji_user -p caiji_db < caiji_backup.sql

# 检查数据库状态
mysql -u root -p -e "SHOW PROCESSLIST;"
mysql -u root -p -e "SHOW ENGINE INNODB STATUS\G"

# 优化数据表
mysql -u caiji_user -p caiji_db -e "OPTIMIZE TABLE collection_tasks;"
```

---

📞 **技术支持**: 如遇到问题，请检查MySQL错误日志或联系技术支持团队。
