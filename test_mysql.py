#!/usr/bin/env python3
"""
MySQL数据库配置测试脚本
"""

import os
import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_mysql_config():
    """测试MySQL配置"""
    print("=== MySQL数据库配置测试 ===\n")
    
    # 显示配置信息
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', '3306'))
    DB_USER = os.environ.get('DB_USER', 'root')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')
    DB_NAME = os.environ.get('DB_NAME', 'caiji_db')
    
    print("📋 当前配置:")
    print(f"   主机: {DB_HOST}")
    print(f"   端口: {DB_PORT}")
    print(f"   用户: {DB_USER}")
    print(f"   密码: {'*' * len(DB_PASSWORD) if DB_PASSWORD else '(空)'}")
    print(f"   数据库: {DB_NAME}")
    print()
    
    # 测试MySQL服务器连接
    print("1. 测试MySQL服务器连接...")
    try:
        connection = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            charset='utf8mb4',
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   ✅ MySQL服务器连接成功")
            print(f"   📋 MySQL版本: {version[0]}")
        
        connection.close()
        
    except pymysql.Error as e:
        print(f"   ❌ MySQL服务器连接失败: {e}")
        print("\n💡 可能的解决方案:")
        print("   1. 检查MySQL服务是否已启动")
        print("   2. 检查用户名和密码是否正确")
        print("   3. 检查主机和端口是否正确")
        print("   4. 检查防火墙设置")
        return False
    
    # 测试数据库连接
    print("\n2. 测试数据库连接...")
    try:
        connection = pymysql.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4',
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()
            print(f"   ✅ 数据库连接成功")
            print(f"   📋 当前数据库: {db_name[0]}")
            
            # 检查数据表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"   📋 数据表数量: {len(tables)}")
            
            if tables:
                print("   📋 数据表列表:")
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    count = cursor.fetchone()
                    print(f"      - {table[0]}: {count[0]} 条记录")
            else:
                print("   ⚠️ 数据库中没有数据表，请运行 python init_db.py")
        
        connection.close()
        
    except pymysql.Error as e:
        if "Unknown database" in str(e):
            print(f"   ❌ 数据库 '{DB_NAME}' 不存在")
            print("   💡 请运行 'python init_mysql.py' 创建数据库")
        else:
            print(f"   ❌ 数据库连接失败: {e}")
        return False
    
    # 测试Flask应用连接
    print("\n3. 测试Flask应用数据库连接...")
    try:
        from app import app
        from database import db
        
        with app.app_context():
            # 测试连接
            result = db.engine.execute("SELECT 1").fetchone()
            print("   ✅ Flask应用数据库连接成功")
            
            # 测试模型
            from models import CollectionTask
            count = CollectionTask.query.count()
            print(f"   📋 CollectionTask表记录数: {count}")
            
    except Exception as e:
        print(f"   ❌ Flask应用数据库连接失败: {e}")
        return False
    
    print("\n✅ 所有测试通过！MySQL数据库配置正确。")
    return True

def show_mysql_commands():
    """显示常用MySQL命令"""
    print("\n📚 常用MySQL命令:")
    print("   启动MySQL服务:")
    print("     Windows: net start mysql")
    print("     Linux/Mac: sudo systemctl start mysql")
    print()
    print("   连接MySQL:")
    print("     mysql -u root -p")
    print()
    print("   创建数据库:")
    print("     CREATE DATABASE caiji_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
    print()
    print("   显示数据库:")
    print("     SHOW DATABASES;")
    print()
    print("   使用数据库:")
    print("     USE caiji_db;")
    print()
    print("   显示数据表:")
    print("     SHOW TABLES;")

def main():
    """主函数"""
    if test_mysql_config():
        print("\n🎉 MySQL配置测试完成！可以正常使用。")
    else:
        print("\n❌ MySQL配置测试失败！")
        show_mysql_commands()

if __name__ == '__main__':
    main()
